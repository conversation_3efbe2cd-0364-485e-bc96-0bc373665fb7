# Dify API 封装使用说明

## 概述

本项目已经封装了 Dify API 的调用方法，位于 `src/api/ai/workflow/index.ts` 文件中。该封装提供了类型安全的 TypeScript 接口，支持工作流运行、图片生成等功能。

## 配置

### 环境变量

在 `.env.local` 文件中配置以下环境变量：

```bash
# Dify API 配置
VITE_DIFY_API_URL='http://dev.cerebro.rzhrt.com.cn:800/v1'
VITE_DIFY_API_KEY='app-OAuwBYNASfndvaYCBn0KyLbG'
```

## API 接口

### DifyApi 类

主要的 API 封装类，提供以下方法：

#### 1. 构造函数

```typescript
import { DifyApi } from '@/api/ai/workflow'

// 使用默认配置（从环境变量读取）
const difyApi = new DifyApi()

// 或者自定义配置
const difyApi = new DifyApi({
  apiUrl: 'https://your-dify-api.com/v1',
  apiKey: 'your-api-key'
})
```

#### 2. runWorkflow - 运行工作流

```typescript
const result = await difyApi.runWorkflow({
  inputs: {
    input: '生成一张猫的图片',
    ratio: '9:16'
  },
  query: '生成一张猫的图片',
  user: 'vue-user-123',
  response_mode: 'blocking',
  tool_parameters: {
    stable_diffusion_image_size: '576x1024',
    stable_diffusion_n: 1
  }
})
```

#### 3. generateImage - 生成单张图片

```typescript
try {
  const imageUrl = await difyApi.generateImage(
    '一只可爱的小猫在花园里玩耍',
    {
      ratio: '9:16', // 可选: '9:16' | '16:9' | '1:1'
      user: 'vue-user-123' // 可选，默认为 'vue-user-123'
    }
  )
  console.log('生成的图片URL:', imageUrl)
} catch (error) {
  console.error('生成失败:', error.message)
}
```

#### 4. generateImages - 批量生成图片

```typescript
try {
  const imageUrls = await difyApi.generateImages(
    '美丽的风景画',
    3, // 生成数量
    {
      ratio: '16:9',
      user: 'vue-user-123',
      onProgress: (index, total, imageUrl) => {
        console.log(`生成进度: ${index}/${total}, 图片URL: ${imageUrl}`)
      }
    }
  )
  console.log('所有图片URL:', imageUrls)
} catch (error) {
  console.error('批量生成失败:', error.message)
}
```

#### 5. updateConfig - 更新配置

```typescript
difyApi.updateConfig({
  apiUrl: 'https://new-api-url.com/v1',
  apiKey: 'new-api-key'
})
```

## 默认实例

项目提供了一个默认的 difyApi 实例，可以直接导入使用：

```typescript
import { difyApi } from '@/api/ai/workflow'

// 直接使用
const imageUrl = await difyApi.generateImage('一朵红玫瑰')
```

## 在 Vue 组件中使用

### 基本用法

```vue
<template>
  <div>
    <input v-model="prompt" placeholder="输入图片描述" />
    <button @click="generateImage" :disabled="loading">
      {{ loading ? '生成中...' : '生成图片' }}
    </button>
    <img v-if="imageUrl" :src="imageUrl" alt="生成的图片" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { difyApi } from '@/api/ai/workflow'

const prompt = ref('')
const imageUrl = ref('')
const loading = ref(false)

const generateImage = async () => {
  if (!prompt.value) return
  
  loading.value = true
  try {
    imageUrl.value = await difyApi.generateImage(prompt.value)
  } catch (error) {
    console.error('生成失败:', error)
    alert('生成失败: ' + error.message)
  } finally {
    loading.value = false
  }
}
</script>
```

### 批量生成示例

```vue
<script setup>
import { ref } from 'vue'
import { difyApi } from '@/api/ai/workflow'

const prompt = ref('')
const images = ref([])
const currentProgress = ref(0)
const totalCount = ref(0)
const loading = ref(false)

const generateMultipleImages = async () => {
  if (!prompt.value) return
  
  loading.value = true
  images.value = []
  currentProgress.value = 0
  totalCount.value = 4
  
  try {
    const imageUrls = await difyApi.generateImages(
      prompt.value,
      totalCount.value,
      {
        ratio: '9:16',
        onProgress: (index, total, imageUrl) => {
          currentProgress.value = index
          images.value.push({
            url: imageUrl,
            prompt: prompt.value,
            timestamp: new Date().toISOString()
          })
        }
      }
    )
    console.log('所有图片生成完成:', imageUrls)
  } catch (error) {
    console.error('批量生成失败:', error)
  } finally {
    loading.value = false
  }
}
</script>
```

## 错误处理

API 封装包含完善的错误处理机制：

1. **配置检查**: 自动检查 API Key 是否配置
2. **网络错误**: 处理网络请求失败
3. **API 错误**: 解析并抛出 API 返回的错误信息
4. **数据格式错误**: 检查返回数据格式是否正确

```typescript
try {
  const imageUrl = await difyApi.generateImage('测试图片')
} catch (error) {
  if (error.message.includes('API Key')) {
    console.error('API Key 配置错误')
  } else if (error.message.includes('网络')) {
    console.error('网络连接失败')
  } else {
    console.error('其他错误:', error.message)
  }
}
```

## 类型定义

### DifyConfig

```typescript
interface DifyConfig {
  apiUrl?: string
  apiKey?: string
}
```

### DifyWorkflowRunRequest

```typescript
interface DifyWorkflowRunRequest {
  inputs: Record<string, any>
  query?: string
  user: string
  response_mode?: 'blocking' | 'streaming'
  tool_parameters?: Record<string, any>
}
```

### DifyWorkflowRunResponse

```typescript
interface DifyWorkflowRunResponse {
  data: {
    id: string
    workflow_id: string
    status: string
    outputs: Record<string, any>
    error?: string
    elapsed_time: number
    total_tokens: number
    created_at: number
  }
  answer?: string
}
```

## 注意事项

1. **API Key 安全**: 不要在前端代码中硬编码 API Key，使用环境变量
2. **错误处理**: 始终使用 try-catch 包装 API 调用
3. **进度回调**: 批量生成时使用 onProgress 回调提供用户反馈
4. **网络超时**: API 调用可能需要较长时间，注意设置合适的超时时间

## 示例文件

参考 `src/views/vision/image/index-with-dify-api.vue` 文件，查看完整的使用示例。
