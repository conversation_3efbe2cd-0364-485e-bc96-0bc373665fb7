# 本地开发环境：本地启动所有项目（前端、后端、APP）时使用，不依赖外部环境
NODE_ENV=development

VITE_DEV=true

# 请求路径

# VITE_BASE_URL='http://api.talent.rzhrt.com.cn'

# VITE_BASE_URL='http://192.168.2.91:48080'
VITE_BASE_URL='http://dev.cerebro.rzhrt.com.cn/talent'
VITE_DIFY_API_URL='http://dev.cerebro.rzhrt.com.cn:800/v1'
VITE_DIFY_API_KEY='app-OAuwBYNASfndvaYCBn0KyLbG'
# VITE_BASE_URL='http://************:48080'
# VITE_BASE_URL='http://************:48080'

# 文件上传类型：server - 后端上传， client - 前端直连上传，仅支持 S3 服务
VITE_UPLOAD_TYPE=server

# 接口地址
VITE_API_URL=/admin-api

# 是否删除debugger
VITE_DROP_DEBUGGER=true

# 是否删除console.log
VITE_DROP_CONSOLE=false

# 是否sourcemap
VITE_SOURCEMAP=false

# 打包路径
VITE_BASE_PATH=/

# 商城H5会员端域名
VITE_MALL_H5_DOMAIN='http://localhost:3000'

# 验证码的开关
VITE_APP_CAPTCHA_ENABLE=false

# GoView域名
VITE_GOVIEW_URL='http://127.0.0.1:3000'