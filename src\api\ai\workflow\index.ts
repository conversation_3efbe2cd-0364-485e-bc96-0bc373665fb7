import request from '@/config/axios'

export const getWorkflowPage = async (params) => {
  return await request.get({ url: '/ai/workflow/page', params })
}

export const getWorkflow = async (id) => {
  return await request.get({ url: '/ai/workflow/get?id=' + id })
}

export const createWorkflow = async (data) => {
  return await request.post({ url: '/ai/workflow/create', data })
}

export const updateWorkflow = async (data) => {
  return await request.put({ url: '/ai/workflow/update', data })
}

export const deleteWorkflow = async (id) => {
  return await request.delete({ url: '/ai/workflow/delete?id=' + id })
}

export const testWorkflow = async (data) => {
  return await request.post({ url: '/ai/workflow/test', data })
}

// ================ Dify API 封装 ================

/**
 * Dify API 配置接口
 */
export interface DifyConfig {
  apiUrl?: string
  apiKey?: string
}

/**
 * Dify 工作流运行请求参数
 */
export interface DifyWorkflowRunRequest {
  inputs: Record<string, any>
  query?: string
  user: string
  response_mode?: 'blocking' | 'streaming'
  tool_parameters?: Record<string, any>
}

/**
 * Dify 工作流运行响应
 */
export interface DifyWorkflowRunResponse {
  data: {
    id: string
    workflow_id: string
    status: string
    outputs: Record<string, any>
    error?: string
    elapsed_time: number
    total_tokens: number
    created_at: number
  }
  answer?: string
}

/**
 * Dify API 错误响应
 */
export interface DifyErrorResponse {
  code: string
  message: string
  status: number
}

/**
 * Dify API 封装类
 */
export class DifyApi {
  private apiUrl: string
  private apiKey: string

  constructor(config?: DifyConfig) {
    this.apiUrl = config?.apiUrl || import.meta.env.VITE_DIFY_API_URL || '/ai'
    this.apiKey = config?.apiKey || import.meta.env.VITE_DIFY_API_KEY || ''
  }

  /**
   * 运行 Dify 工作流
   * @param params 工作流运行参数
   * @returns Promise<DifyWorkflowRunResponse>
   */
  async runWorkflow(params: DifyWorkflowRunRequest): Promise<DifyWorkflowRunResponse> {
    if (!this.apiKey) {
      throw new Error('Dify API Key 未配置，请检查环境变量 VITE_DIFY_API_KEY')
    }

    const url = `${this.apiUrl}/workflows/run`

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.apiKey}`
        },
        body: JSON.stringify(params)
      })

      if (!response.ok) {
        const errorData: DifyErrorResponse = await response.json()
        throw new Error(errorData.message || `API 请求失败: ${response.status}`)
      }

      const result: DifyWorkflowRunResponse = await response.json()
      return result
    } catch (error) {
      console.error('Dify 工作流运行失败:', error)
      throw error
    }
  }

  /**
   * 生成图片 - 基于 Stable Diffusion
   * @param prompt 图片描述
   * @param options 生成选项
   * @returns Promise<string> 返回图片URL
   */
  async generateImage(
    prompt: string,
    options: {
      ratio?: '9:16' | '16:9' | '1:1'
      user?: string
    } = {}
  ): Promise<string> {
    const { ratio = '9:16', user = 'vue-user-123' } = options

    const imageSize = ratio === '16:9' ? '1024x576' : ratio === '1:1' ? '1024x1024' : '576x1024'

    const params: DifyWorkflowRunRequest = {
      inputs: {
        input: prompt,
        ratio: ratio
      },
      query: prompt,
      user: user,
      response_mode: 'blocking',
      tool_parameters: {
        stable_diffusion_image_size: imageSize,
        stable_diffusion_n: 1
      }
    }

    const result = await this.runWorkflow(params)

    if (result.data?.outputs?.img_url) {
      return result.data.outputs.img_url
    } else if (result.answer) {
      throw new Error(`生成失败: ${result.answer}`)
    } else {
      throw new Error('API 返回的数据格式不正确，未能找到图片URL')
    }
  }

  /**
   * 批量生成图片
   * @param prompt 图片描述
   * @param count 生成数量
   * @param options 生成选项
   * @returns Promise<string[]> 返回图片URL数组
   */
  async generateImages(
    prompt: string,
    count: number,
    options: {
      ratio?: '9:16' | '16:9' | '1:1'
      user?: string
      onProgress?: (index: number, total: number, imageUrl: string) => void
    } = {}
  ): Promise<string[]> {
    const { onProgress } = options
    const imageUrls: string[] = []

    for (let i = 0; i < count; i++) {
      try {
        const imageUrl = await this.generateImage(prompt, options)
        imageUrls.push(imageUrl)

        // 调用进度回调
        if (onProgress) {
          onProgress(i + 1, count, imageUrl)
        }
      } catch (error) {
        console.error(`生成第 ${i + 1} 张图片失败:`, error)
        // 继续生成下一张，不中断整个流程
      }
    }

    return imageUrls
  }

  /**
   * 更新配置
   * @param config 新的配置
   */
  updateConfig(config: DifyConfig): void {
    if (config.apiUrl) this.apiUrl = config.apiUrl
    if (config.apiKey) this.apiKey = config.apiKey
  }
}

/**
 * 默认的 Dify API 实例
 */
export const difyApi = new DifyApi()
