/**
 * Dify API 测试工具
 * 用于测试封装的 Dify API 是否正常工作
 */

import { difyApi } from '@/api/ai/workflow'

/**
 * 测试单张图片生成
 */
export async function testSingleImageGeneration() {
  console.log('开始测试单张图片生成...')
  
  try {
    const imageUrl = await difyApi.generateImage(
      '一只可爱的小猫在花园里玩耍',
      {
        ratio: '9:16',
        user: 'test-user'
      }
    )
    
    console.log('✅ 单张图片生成成功:', imageUrl)
    return imageUrl
  } catch (error) {
    console.error('❌ 单张图片生成失败:', error.message)
    throw error
  }
}

/**
 * 测试批量图片生成
 */
export async function testBatchImageGeneration() {
  console.log('开始测试批量图片生成...')
  
  try {
    const imageUrls = await difyApi.generateImages(
      '美丽的风景画',
      2, // 生成2张图片
      {
        ratio: '16:9',
        user: 'test-user',
        onProgress: (index, total, imageUrl) => {
          console.log(`📊 生成进度: ${index}/${total}, 图片URL: ${imageUrl}`)
        }
      }
    )
    
    console.log('✅ 批量图片生成成功:', imageUrls)
    return imageUrls
  } catch (error) {
    console.error('❌ 批量图片生成失败:', error.message)
    throw error
  }
}

/**
 * 测试工作流运行
 */
export async function testWorkflowRun() {
  console.log('开始测试工作流运行...')
  
  try {
    const result = await difyApi.runWorkflow({
      inputs: {
        input: '生成一张测试图片',
        ratio: '1:1'
      },
      query: '生成一张测试图片',
      user: 'test-user',
      response_mode: 'blocking',
      tool_parameters: {
        stable_diffusion_image_size: '1024x1024',
        stable_diffusion_n: 1
      }
    })
    
    console.log('✅ 工作流运行成功:', result)
    return result
  } catch (error) {
    console.error('❌ 工作流运行失败:', error.message)
    throw error
  }
}

/**
 * 测试配置更新
 */
export function testConfigUpdate() {
  console.log('开始测试配置更新...')
  
  try {
    // 保存原始配置
    const originalConfig = {
      apiUrl: difyApi.apiUrl,
      apiKey: difyApi.apiKey
    }
    
    // 更新配置
    difyApi.updateConfig({
      apiUrl: 'https://test-api.example.com/v1',
      apiKey: 'test-api-key'
    })
    
    console.log('✅ 配置更新成功')
    
    // 恢复原始配置
    difyApi.updateConfig(originalConfig)
    console.log('✅ 配置恢复成功')
    
    return true
  } catch (error) {
    console.error('❌ 配置更新失败:', error.message)
    throw error
  }
}

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log('🚀 开始运行 Dify API 测试套件...')
  
  const results = {
    configUpdate: false,
    singleImage: false,
    batchImages: false,
    workflowRun: false
  }
  
  try {
    // 测试配置更新
    results.configUpdate = testConfigUpdate()
    console.log('✅ 配置更新测试通过')
  } catch (error) {
    console.error('❌ 配置更新测试失败')
  }
  
  try {
    // 测试单张图片生成
    await testSingleImageGeneration()
    results.singleImage = true
    console.log('✅ 单张图片生成测试通过')
  } catch (error) {
    console.error('❌ 单张图片生成测试失败')
  }
  
  try {
    // 测试批量图片生成
    await testBatchImageGeneration()
    results.batchImages = true
    console.log('✅ 批量图片生成测试通过')
  } catch (error) {
    console.error('❌ 批量图片生成测试失败')
  }
  
  try {
    // 测试工作流运行
    await testWorkflowRun()
    results.workflowRun = true
    console.log('✅ 工作流运行测试通过')
  } catch (error) {
    console.error('❌ 工作流运行测试失败')
  }
  
  // 输出测试结果
  console.log('📊 测试结果汇总:')
  console.table(results)
  
  const passedTests = Object.values(results).filter(Boolean).length
  const totalTests = Object.keys(results).length
  
  console.log(`🎯 测试通过率: ${passedTests}/${totalTests} (${Math.round(passedTests / totalTests * 100)}%)`)
  
  return results
}

/**
 * 在浏览器控制台中运行测试
 * 使用方法：
 * 1. 在浏览器中打开项目
 * 2. 打开开发者工具控制台
 * 3. 输入：window.testDifyApi()
 */
if (typeof window !== 'undefined') {
  window.testDifyApi = runAllTests
  window.testDifyApiSingle = testSingleImageGeneration
  window.testDifyApiBatch = testBatchImageGeneration
  window.testDifyApiWorkflow = testWorkflowRun
  
  console.log('🔧 Dify API 测试工具已加载')
  console.log('使用 window.testDifyApi() 运行所有测试')
  console.log('使用 window.testDifyApiSingle() 测试单张图片生成')
  console.log('使用 window.testDifyApiBatch() 测试批量图片生成')
  console.log('使用 window.testDifyApiWorkflow() 测试工作流运行')
}
