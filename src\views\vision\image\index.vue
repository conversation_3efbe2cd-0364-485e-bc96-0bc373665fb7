<template>
  <div id="image-generator-page">
    <div class="settings-panel text-white rounded-2">
      <div class="panel-content">
        <div class="section">
          <label class="section-title">图片描述</label>
          <div class="input-wrapper">
            <span>主词</span>
            <input
              type="text"
              v-model="formData.mainSubject"
              placeholder="如: 人, 动物, 植物及物体等"
            />
          </div>
          <div class="input-wrapper textarea-wrapper">
            <span>场景</span>
            <div class="textarea-container">
              <textarea v-model="formData.scene" placeholder="对主体所处环境的细节描述"></textarea>
              <div class="ai-optimize" @click="handleAiOptimize">
                <div class="ai-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path
                      d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"
                    />
                  </svg>
                </div>
                <span class="ai-text">AI 优化</span>
                <span class="ai-count">1/500</span>
              </div>
            </div>
          </div>
        </div>

        <div class="section">
          <label class="section-title">参数设置</label>
          <div class="param-row">
            <span>图片比例</span>
            <el-radio-group v-model="formData.aspectRatio" class="custom-radio-group">
              <el-radio value="9:16" class="custom-radio">9:16</el-radio>
              <el-radio value="16:9" class="custom-radio">16:9</el-radio>
            </el-radio-group>
          </div>
          <div class="param-row">
            <span>生成数量</span>
            <el-radio-group v-model="formData.count" class="custom-radio-group">
              <el-radio :value="1" class="custom-radio">1条</el-radio>
              <el-radio :value="2" class="custom-radio">2条</el-radio>
              <el-radio :value="3" class="custom-radio">3条</el-radio>
              <el-radio :value="4" class="custom-radio">4条</el-radio>
            </el-radio-group>
          </div>
        </div>
      </div>

      <button @click="handleGenerate" :disabled="isLoading || isGenerating" class="generate-btn">
        <span v-if="!isLoading && !isGenerating">立即生成</span>
        <span v-else-if="isGenerating && formData.count > 1"
          >生成中 {{ currentGenerationIndex + 1 }}/{{ formData.count }}</span
        >
        <span v-else>生成中...</span>
      </button>
    </div>

    <div class="result-panel" :class="{ 'with-history': showHistoryPanel }">
      <div v-if="error" class="error-state">
        <p>出错了：{{ error }}</p>
      </div>
      <div v-else-if="currentDisplayImage || isGenerating" class="image-display">
        <div v-if="currentDisplayImage" class="current-image-wrapper">
          <img
            :src="currentDisplayImage.url || currentDisplayImage"
            alt="Current Generated Image"
          />
        </div>
        <div v-else-if="isGenerating" class="generating-placeholder">
          <div class="placeholder-content">
            <div class="spinner"></div>
            <p>正在生成图片...</p>
          </div>
        </div>
      </div>
      <div v-else class="initial-state">
        <p>请在左侧输入描述并设置参数</p>
      </div>

      <!-- 多条生成时的loading指示器 -->
      <div
        v-if="(isGenerating || showCompletedProgress) && formData.count > 1"
        class="generation-progress"
      >
        <div class="progress-boxes">
          <div
            v-for="n in formData.count"
            :key="n"
            class="progress-box"
            :class="{
              completed:
                (isGenerating && n <= currentGenerationIndex) ||
                (!isGenerating && generatedImages[n - 1]),
              loading: isGenerating && n === currentGenerationIndex + 1,
              pending:
                (isGenerating && n > currentGenerationIndex + 1) ||
                (!isGenerating && !generatedImages[n - 1]),
              clickable: generatedImages[n - 1],
              active:
                generatedImages[n - 1] &&
                currentDisplayImage &&
                (generatedImages[n - 1].url || generatedImages[n - 1]) ===
                  (currentDisplayImage.url || currentDisplayImage)
            }"
            @click="generatedImages[n - 1] && selectProgressImage(n - 1)"
          >
            <!-- 已完成的显示图片预览 -->
            <img
              v-if="generatedImages[n - 1]"
              :src="generatedImages[n - 1].url || generatedImages[n - 1]"
              :alt="`生成的图片 ${n}`"
              class="progress-preview"
            />
            <!-- 正在生成的显示loading -->
            <div v-else-if="n === currentGenerationIndex + 1" class="box-spinner"></div>
          </div>
        </div>
        <p class="progress-text">
          <span v-if="isGenerating"
            >正在生成第 {{ currentGenerationIndex + 1 }} / {{ formData.count }} 张图片...</span
          >
          <span v-else>已完成 {{ generatedImages.length }} / {{ formData.count }} 张图片生成</span>
        </p>
      </div>

      <!-- 单条生成时的loading -->
      <div v-else-if="isGenerating" class="loading-state">
        <div class="spinner"></div>
        <p>正在拼命加载中, 请稍候...</p>
      </div>
    </div>

    <!-- 历史图片列表面板 -->
    <div v-if="showHistoryPanel" class="history-panel">
      <div class="history-header">
        <h3>图片列表</h3>
        <div class="history-controls">
          <button @click="scrollHistory('up')" class="scroll-btn" :disabled="historyScrollTop <= 0">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M7 14l5-5 5 5z" />
            </svg>
          </button>
          <button
            @click="scrollHistory('down')"
            class="scroll-btn"
            :disabled="historyScrollTop >= maxScrollTop"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M7 10l5 5 5-5z" />
            </svg>
          </button>
        </div>
      </div>
      <div class="history-content" ref="historyContainer" @scroll="updateScrollState">
        <div
          v-for="(img, index) in historyImagesList"
          :key="index"
          class="history-image-wrapper"
          :class="{
            active:
              currentDisplayImage &&
              (img.url || img) === (currentDisplayImage.url || currentDisplayImage)
          }"
          @click="selectHistoryImage(img)"
        >
          <img :src="img.url || img" :alt="`历史图片 ${index + 1}`" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted, watch } from 'vue'
import { difyApi } from '@/api/ai/workflow'

// 表单响应式数据
const formData = ref({
  mainSubject: '',
  scene: '',
  aspectRatio: '9:16',
  count: 1
})

// 其他状态
const isLoading = ref(false)
const isGenerating = ref(false)
const currentGenerationIndex = ref(0)
const showCompletedProgress = ref(false)
const error = ref(null)
const generatedImages = ref([])
const currentDisplayImage = ref(null) // 当前显示的图片

// 历史图片列表相关状态
const historyImagesList = ref([])
const historyContainer = ref(null)
const historyScrollTop = ref(0)
const maxScrollTop = ref(0)

// 添加测试数据（可以删除）
// historyImagesList.value = [
//   { url: 'https://picsum.photos/400/400?random=1', prompt: '测试图片1', timestamp: new Date().toISOString() },
//   { url: 'https://picsum.photos/400/400?random=2', prompt: '测试图片2', timestamp: new Date().toISOString() },
//   { url: 'https://picsum.photos/400/400?random=3', prompt: '测试图片3', timestamp: new Date().toISOString() }
// ]

// 计算属性：是否显示历史面板（生成第二张图片后显示）
const showHistoryPanel = computed(() => {
  return historyImagesList.value.length >= 2
})

// 拼接完整的 prompt
const fullPrompt = computed(() => {
  return `${formData.value.mainSubject}, ${formData.value.scene}`
})

// 主生成函数 - 使用封装的 difyApi
const handleGenerate = async () => {
  if (!formData.value.mainSubject && !formData.value.scene) {
    alert('主词和场景至少需要填写一个！')
    return
  }

  isLoading.value = true
  isGenerating.value = true
  currentGenerationIndex.value = 0
  showCompletedProgress.value = false
  error.value = null
  generatedImages.value = []
  currentDisplayImage.value = null

  try {
    // 使用封装的 difyApi 批量生成图片
    const imageUrls = await difyApi.generateImages(fullPrompt.value, formData.value.count, {
      ratio: formData.value.aspectRatio,
      user: 'vue-user-123',
      onProgress: (index, total, imageUrl) => {
        // 更新当前生成索引
        currentGenerationIndex.value = index - 1

        // 创建图片数据对象
        const imageData = {
          url: imageUrl,
          prompt: fullPrompt.value,
          timestamp: new Date().toISOString()
        }

        // 添加到当前显示的图片列表
        generatedImages.value.push(imageData)

        // 设置为当前显示的图片（总是显示最新生成的）
        currentDisplayImage.value = imageData

        // 添加到历史列表
        historyImagesList.value.push(imageData)

        // 更新滚动状态
        nextTick(() => {
          updateScrollState()
        })
      }
    })

    console.log(`成功生成 ${imageUrls.length} 张图片`)
  } catch (err) {
    error.value = err.message
    console.error('图片生成失败:', err)
  } finally {
    isLoading.value = false
    isGenerating.value = false

    // 如果生成了多张图片，显示完成的进度条3秒钟
    if (formData.value.count > 1 && generatedImages.value.length > 0) {
      showCompletedProgress.value = true
      setTimeout(() => {
        showCompletedProgress.value = false
      }, 3000)
    }

    currentGenerationIndex.value = 0
  }
}

// 历史图片列表相关方法
const scrollHistory = (direction) => {
  if (!historyContainer.value) return

  const scrollAmount = 120 // 每次滚动的像素
  if (direction === 'up') {
    historyScrollTop.value = Math.max(0, historyScrollTop.value - scrollAmount)
  } else {
    historyScrollTop.value = Math.min(maxScrollTop.value, historyScrollTop.value + scrollAmount)
  }

  historyContainer.value.scrollTop = historyScrollTop.value
}

const updateScrollState = () => {
  if (!historyContainer.value) return

  const container = historyContainer.value
  historyScrollTop.value = container.scrollTop
  maxScrollTop.value = container.scrollHeight - container.clientHeight
}

const selectHistoryImage = (img) => {
  // 点击历史图片时，将其设置为当前显示的图片
  currentDisplayImage.value = img
}

const selectProgressImage = (index) => {
  // 点击进度框中的图片时，将其设置为当前显示的图片
  if (generatedImages.value[index]) {
    currentDisplayImage.value = generatedImages.value[index]
  }
}

// 监听历史列表变化，更新滚动状态
watch(
  historyImagesList,
  async () => {
    await nextTick()
    updateScrollState()
  },
  { deep: true }
)

// 组件挂载后初始化滚动状态
onMounted(() => {
  updateScrollState()
})

function handleAiOptimize() {}
</script>

<style>
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

:root {
  --primary-bg: #1a1625;
  --secondary-bg: #2a253a;
  --panel-bg: #211e30;
  --text-color: #e0e0e0;
  --text-secondary-color: #a09cb0;
  --accent-color: #6c5ce7;
  --accent-hover-color: #5a4bd7;
  --border-color: #3a364f;
  --input-bg: #3a364f;
}

#image-generator-page {
  position: relative;
  display: flex;
  width: 100%;
  height: calc(100vh - 165px);
}

/* 左侧面板 */
.settings-panel {
  display: flex;
  width: 380px;
  padding: 24px;
  margin: 16px;
  background: linear-gradient(135deg, var(--panel-bg) 0%, rgb(42 37 58 / 95%) 100%);
  border: 1px solid rgb(255 255 255 / 10%);
  border-right: 1px solid var(--border-color);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgb(0 0 0 / 30%);
  flex-direction: column;
  backdrop-filter: blur(10px);
}

.panel-content {
  flex-grow: 1;
}

/* 区域样式 */
.section {
  margin-bottom: 32px;
}

.section-title {
  display: block;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: var(--text-color);
}

/* 文本域容器样式 */
.textarea-wrapper {
  position: relative;
}

.textarea-container {
  position: relative;
  flex-grow: 1;
  width: 100%;
}

/* AI优化按钮样式 */
.ai-optimize {
  position: absolute;
  right: 8px;
  bottom: 8px;
  display: flex;
  padding: 6px 10px;
  cursor: pointer;
  background: rgb(108 92 231 / 90%);
  border-radius: 6px;
  transition: all 0.3s ease;
  align-items: center;
  gap: 6px;
}

.ai-optimize:hover {
  background: rgb(108 92 231 / 100%);
  transform: scale(1.05);
}

.ai-optimize .ai-icon {
  display: flex;
  width: 16px;
  height: 16px;
  color: white;
  align-items: center;
  justify-content: center;
}

.ai-optimize .ai-text {
  font-size: 12px;
  font-weight: 600;
  color: white;
}

.ai-optimize .ai-count {
  font-size: 10px;
  color: rgb(255 255 255 / 80%);
}

.input-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 12px;
}

.input-wrapper span {
  width: 50px;
  padding-top: 8px;
  font-size: 14px;
  color: var(--text-secondary-color);
}

.input-wrapper input[type='text'],
.input-wrapper textarea {
  width: 100%;
  padding: 12px 16px;
  font-size: 14px;
  color: var(--text-color);
  background: rgb(255 255 255 / 8%);
  border: 1px solid rgb(255 255 255 / 15%);
  border-radius: 10px;
  outline: none;
  transition: all 0.3s ease;
  flex-grow: 1;
  backdrop-filter: blur(5px);
}

.input-wrapper input[type='text']:focus,
.input-wrapper textarea:focus {
  background: rgb(255 255 255 / 12%);
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgb(108 92 231 / 20%);
}

.input-wrapper input[type='text']::placeholder,
.input-wrapper textarea::placeholder {
  color: var(--text-secondary-color);
  opacity: 0.7;
}

.input-wrapper textarea {
  height: 100px;
  padding-bottom: 40px; /* 为AI优化按钮留出空间 */
  font-family: inherit;
  resize: vertical;
}

.textarea-wrapper .textarea-container textarea {
  width: 100%;
  height: 100px;
  padding: 12px 16px 40px; /* 底部留出更多空间给AI按钮 */
}

.param-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.param-row > span {
  color: var(--text-secondary-color);
}

/* Element Plus Radio 自定义样式 */
.custom-radio-group {
  display: flex;
  gap: 8px;
}

.custom-radio {
  margin-right: 0 !important;
}

.custom-radio :deep(.el-radio__input) {
  display: none;
}

.custom-radio :deep(.el-radio__label) {
  position: relative;
  padding: 8px 16px;
  font-size: 14px;
  color: var(--text-secondary-color);
  cursor: pointer;
  background-color: var(--input-bg);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  transition: all 0.3s;
}

.custom-radio.is-checked :deep(.el-radio__label) {
  position: relative;
  color: white;
  background: linear-gradient(90deg, #675dff 0%, #bf78e6 100%);
  border: none;
  border-radius: 20px;
}

.custom-radio.is-checked :deep(.el-radio__label)::before {
  position: absolute;
  inset: -5px;
  z-index: -1;
  background: linear-gradient(90deg, #675dff 0%, #bf78e6 100%);
  border-radius: 25px;
  content: '';
}

.custom-radio:not(.is-checked) :deep(.el-radio__label):hover {
  position: relative;
  color: white;
  background: linear-gradient(90deg, #675dff 0%, #bf78e6 100%);
  border: none;
}

.custom-radio:not(.is-checked) :deep(.el-radio__label):hover::before {
  position: absolute;
  inset: -2px;
  z-index: -1;
  background: linear-gradient(90deg, #675dff 0%, #bf78e6 100%);
  border-radius: 22px;
  content: '';
}

.generate-btn {
  position: relative;
  width: 100%;
  padding: 16px 24px;
  overflow: hidden;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: white;
  cursor: pointer;
  background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover-color) 100%);
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(108 92 231 / 30%);
  transition: all 0.3s ease;
}

.generate-btn::before {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgb(255 255 255 / 20%), transparent);
  content: '';
  transition: left 0.5s;
}

.generate-btn:hover::before {
  left: 100%;
}

.generate-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgb(108 92 231 / 40%);
}

.generate-btn:active {
  transform: translateY(0);
}

.generate-btn:disabled {
  cursor: not-allowed;
  background: linear-gradient(135deg, #555 0%, #444 100%);
  transform: none;
  box-shadow: none;
}

/* 右侧结果区 */
.result-panel {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  overflow-y: auto;
}

.initial-state p,
.error-state p {
  font-size: 18px;
  color: var(--text-secondary-color);
}

.error-state p {
  color: #ff6b6b;
}

/* 单图显示区域 */
.image-display {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.current-image-wrapper {
  position: relative;
  max-width: 90%;
  max-height: 90%;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgb(0 0 0 / 30%);
}

.current-image-wrapper img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 生成中的占位符 */
.generating-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.placeholder-content {
  color: var(--text-secondary-color);
  text-align: center;
}

.placeholder-content .spinner {
  margin: 0 auto 16px;
}

.placeholder-content p {
  margin: 0;
  font-size: 16px;
}

/* 加载动画 */
.loading-state {
  text-align: center;
}

.spinner {
  width: 50px;
  height: 50px;
  margin: 0 auto 20px;
  border: 4px solid var(--border-color);
  border-top-color: var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 历史图片面板 */
.history-panel {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  width: 200px;
  height: 100vh;
  background-color: var(--panel-bg);
  border-left: 1px solid var(--border-color);
  flex-direction: column;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
}

.history-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: bold;
  color: var(--text-color);
}

.history-controls {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.scroll-btn {
  display: flex;
  width: 24px;
  height: 24px;
  color: var(--text-secondary-color);
  cursor: pointer;
  background: var(--input-bg);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  transition: all 0.2s;
  align-items: center;
  justify-content: center;
}

.scroll-btn:hover:not(:disabled) {
  color: white;
  background: var(--accent-color);
}

.scroll-btn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.history-content {
  display: flex;
  padding: 8px;
  overflow-y: auto;
  flex: 1;
  flex-direction: column;
  gap: 8px;
}

.history-content::-webkit-scrollbar {
  width: 4px;
}

.history-content::-webkit-scrollbar-track {
  background: var(--input-bg);
}

.history-content::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 2px;
}

.history-content::-webkit-scrollbar-thumb:hover {
  background: var(--accent-color);
}

.history-image-wrapper {
  position: relative;
  width: 100%;
  padding-top: 100%; /* 1:1 Aspect Ratio */
  overflow: hidden;
  cursor: pointer;
  background-color: var(--secondary-bg);
  border-radius: 6px;
  transition:
    transform 0.2s,
    box-shadow 0.2s;
}

.history-image-wrapper:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgb(108 92 231 / 30%);
}

.history-image-wrapper.active {
  border: 3px solid #675dff;
  box-shadow: 0 0 0 2px rgb(103 93 255 / 30%);
}

.history-image-wrapper img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 多条生成进度指示器 */
.generation-progress {
  position: absolute;
  bottom: 20px;
  left: 50%;
  z-index: 10;
  text-align: center;
  transform: translateX(-50%);
}

.progress-boxes {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 12px;
}

.progress-box {
  position: relative;
  display: flex;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  transition: all 0.3s ease;
  align-items: center;
  justify-content: center;
}

.progress-box.completed {
  overflow: hidden;
  background: linear-gradient(135deg, #675dff 0%, #bf78e6 100%);
  box-shadow: 0 4px 12px rgb(103 93 255 / 40%);
}

.progress-box.loading {
  background: rgb(103 93 255 / 20%);
  border: 2px solid #675dff;
}

.progress-box.pending {
  background: rgb(255 255 255 / 10%);
  border: 2px solid rgb(255 255 255 / 30%);
}

.progress-box.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.progress-box.clickable:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgb(103 93 255 / 60%);
}

.progress-box.active {
  border: 3px solid #675dff !important;
  box-shadow: 0 0 0 2px rgb(103 93 255 / 30%) !important;
}

.box-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgb(103 93 255 / 30%);
  border-top: 2px solid #675dff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.progress-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.progress-text {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
}

/* 调整主内容区域，为历史面板留出空间 */
.result-panel {
  position: relative;
  transition: margin-right 0.3s ease;
}

/* 当显示历史面板时，调整主内容区域 */
.result-panel.with-history {
  margin-right: 200px;
}

/* 全局和根样式 */
</style>
